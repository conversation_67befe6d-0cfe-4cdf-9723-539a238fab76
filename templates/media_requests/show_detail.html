{% extends 'base.html' %}

{% block title %}{{ tv_show.title }} - Overseerr Dashboard{% endblock %}

{% block extra_css %}
<style>
    /* Toast styling */
    .toast {
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .toast-header.bg-success-subtle {
        border-bottom: 1px solid rgba(25, 135, 84, 0.2);
    }

    .toast-header.bg-danger-subtle {
        border-bottom: 1px solid rgba(220, 53, 69, 0.2);
    }

    .toast-header.bg-warning-subtle {
        border-bottom: 1px solid rgba(255, 193, 7, 0.2);
    }

    .toast-header.bg-info-subtle {
        border-bottom: 1px solid rgba(13, 202, 240, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<!-- Show Header -->
<div class="row mb-4">
    <div class="col-md-3">
        {% if tv_show.poster_path %}
            <img src="https://image.tmdb.org/t/p/w500{{ tv_show.poster_path }}"
                 class="img-fluid rounded shadow" alt="{{ tv_show.title }}">
        {% else %}
            <div class="bg-light rounded shadow d-flex align-items-center justify-content-center"
                 style="height: 450px;">
                <i class="bi bi-tv text-muted" style="font-size: 4rem;"></i>
            </div>
        {% endif %}
    </div>
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <h1 class="display-5">{{ tv_show.title }}</h1>
            <div class="d-flex align-items-center gap-2">
                <div class="dropdown">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-sliders me-1"></i>
                        Profile: {{ tv_show.quality_profile.name|default:"None" }}
                    </button>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">Quality Profiles</h6></li>
                        <li><a class="dropdown-item {% if not tv_show.quality_profile %}active{% endif %}"
                               href="#" onclick="assignProfile(null)">
                            <i class="bi bi-x-circle me-1"></i>No Profile
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <!-- We'll populate this with JavaScript -->
                        <div id="profileDropdownItems"></div>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/profiles/" target="_blank">
                            <i class="bi bi-gear me-1"></i>Manage Profiles
                        </a></li>
                    </ul>
                </div>
                <span class="badge fs-6
                    {% if tv_show.availability_status == 'available' %}bg-success
                    {% elif tv_show.availability_status == 'partial' %}bg-warning
                    {% else %}bg-danger{% endif %}">
                    {% if tv_show.availability_status == 'available' %}
                        <i class="bi bi-check-circle me-1"></i>Available
                    {% elif tv_show.availability_status == 'partial' %}
                        <i class="bi bi-clock me-1"></i>Partially Available
                    {% else %}
                        <i class="bi bi-x-circle me-1"></i>Unavailable
                    {% endif %}
                </span>
            </div>
        </div>

        <!-- Show Info -->
        <div class="row mb-3">
            <div class="col-md-6">
                {% if tv_show.first_air_date %}
                    <p class="mb-1">
                        <strong><i class="bi bi-calendar me-1"></i>First Aired:</strong>
                        {{ tv_show.first_air_date|date:"F d, Y" }}
                    </p>
                {% endif %}
                {% if tv_show.last_air_date %}
                    <p class="mb-1">
                        <strong><i class="bi bi-calendar-check me-1"></i>Last Aired:</strong>
                        {{ tv_show.last_air_date|date:"F d, Y" }}
                    </p>
                {% endif %}
                {% if tv_show.status %}
                    <p class="mb-1">
                        <strong><i class="bi bi-info-circle me-1"></i>Status:</strong>
                        {{ tv_show.status }}
                    </p>
                {% endif %}
            </div>
            <div class="col-md-6">
                <p class="mb-1">
                    <strong><i class="bi bi-collection me-1"></i>Seasons:</strong>
                    {{ tv_show.get_available_seasons }}/{{ tv_show.get_total_seasons }} available
                </p>
                <p class="mb-1">
                    <strong><i class="bi bi-play me-1"></i>Episodes:</strong>
                    {{ tv_show.get_available_episodes }}/{{ tv_show.get_total_episodes }} available
                </p>
                {% if tv_show.media_request %}
                    <p class="mb-1">
                        <strong><i class="bi bi-person me-1"></i>Requested by:</strong>
                        {{ tv_show.media_request.requested_by }}
                    </p>
                    <p class="mb-1">
                        <strong><i class="bi bi-clock me-1"></i>Requested:</strong>
                        {{ tv_show.media_request.requested_at|date:"F d, Y" }}
                    </p>
                {% endif %}
            </div>
        </div>

        <!-- Overview -->
        {% if tv_show.overview %}
            <div class="mb-3">
                <h5>Overview</h5>
                <p class="text-muted">{{ tv_show.overview }}</p>
            </div>
        {% endif %}

        <!-- Back Button -->
        <a href="{% url 'dashboard' %}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
        </a>
    </div>
</div>

<!-- Seasons -->
<div class="card">
    <div class="card-header">
        <h4 class="mb-0">
            <i class="bi bi-collection me-2"></i>Seasons
        </h4>
    </div>
    <div class="card-body p-0">
        {% if seasons %}
            <div class="accordion" id="seasonsAccordion">
                {% for season in seasons %}
                    <div class="accordion-item">
                        <h2 class="accordion-header position-relative" id="season{{ season.id }}Header">
                            <button class="accordion-button collapsed season-header" type="button"
                                    data-bs-toggle="collapse" data-bs-target="#season{{ season.id }}"
                                    aria-expanded="false" aria-controls="season{{ season.id }}">
                                <div class="d-flex justify-content-between align-items-center w-100 me-5">
                                    <div>
                                        <strong>{{ season.name }}</strong>
                                        {% if season.air_date %}
                                            <small class="text-muted ms-2">
                                                <i class="bi bi-calendar me-1"></i>{{ season.air_date|date:"Y" }}
                                            </small>
                                        {% endif %}
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge me-2
                                            {% if season.availability_status == 'available' %}bg-success
                                            {% elif season.availability_status == 'partial' %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ season.available_episodes }}/{{ season.total_episodes }} episodes
                                        </span>
                                        {% if season.quality %}
                                            <span class="badge bg-info quality-badge me-2">
                                                {{ season.quality.resolution }} {{ season.quality.source }}
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </button>
                            <div class="position-absolute top-50 end-0 translate-middle-y me-5 d-flex gap-1" style="z-index: 10;">
                                <button class="btn btn-sm btn-outline-success"
                                        onclick="autoSearchSeason('{{ tv_show.title|escapejs }}', {{ season.season_number }}, {{ tv_show.id }});"
                                        title="Automatic search - select best torrent">
                                    <i class="bi bi-search"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary"
                                        onclick="searchSeason('{{ tv_show.title|escapejs }}', {{ season.season_number }});"
                                        title="Manual search - browse all torrents">
                                    <i class="bi bi-person"></i>
                                </button>
                            </div>
                        </h2>
                        <div id="season{{ season.id }}" class="accordion-collapse collapse"
                             aria-labelledby="season{{ season.id }}Header" data-bs-parent="#seasonsAccordion">
                            <div class="accordion-body p-0">
                                <div class="episodes-container" data-season-id="{{ season.id }}">
                                    <div class="text-center p-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Loading episodes...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center p-4">
                <i class="bi bi-collection text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">No Seasons Found</h5>
                <p class="text-muted">This show doesn't have any season data yet.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Jackett Search Modal -->
<div class="modal fade" id="jackettSearchModal" tabindex="-1" aria-labelledby="jackettSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="jackettSearchModalLabel">
                    <i class="bi bi-search me-2"></i><span id="searchModalTitle">Search</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="searchInfo" class="alert alert-info mb-3" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-info-circle me-2"></i>
                            <span id="searchQuery"></span>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="filterToggle" checked>
                            <label class="form-check-label" for="filterToggle">
                                <small>Filter relevant only</small>
                            </label>
                        </div>
                    </div>
                </div>

                <div id="searchLoading" class="text-center p-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Searching...</span>
                    </div>
                    <p class="mt-2 text-muted">Searching indexers...</p>
                </div>

                <div id="searchError" class="alert alert-danger" style="display: none;">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="searchErrorMessage"></span>
                </div>

                <div id="searchResults" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Search Results</h6>
                        <div class="d-flex align-items-center gap-2">
                            <button id="showAllResultsBtn" class="btn btn-sm btn-outline-secondary"
                                    style="display: none;" onclick="toggleFilter()">
                                <i class="bi bi-funnel"></i> Show All Results
                            </button>
                            <span id="resultsCount" class="badge bg-primary"></span>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Title</th>
                                    <th>Quality</th>
                                    <th>Size</th>
                                    <th>Seeders</th>
                                    <th>Leechers</th>
                                    <th>Indexer</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="noResults" class="text-center p-4" style="display: none;">
                    <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                    <h5 class="mt-3 text-muted">No Results Found</h5>
                    <p class="text-muted">No torrents found for this episode. Try searching manually or check your indexers.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="toastIcon" class="bi bi-check-circle-fill text-success me-2"></i>
            <strong id="toastTitle" class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            Operation completed successfully.
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    // Load episodes when season is expanded
    document.addEventListener('DOMContentLoaded', function() {
        const accordionButtons = document.querySelectorAll('.accordion-button');

        accordionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const target = this.getAttribute('data-bs-target');
                const seasonElement = document.querySelector(target);
                const episodesContainer = seasonElement.querySelector('.episodes-container');
                const seasonId = episodesContainer.getAttribute('data-season-id');

                // Only load if not already loaded
                if (!episodesContainer.hasAttribute('data-loaded')) {
                    loadEpisodes(seasonId, episodesContainer);
                }
            });
        });
    });

    async function loadEpisodes(seasonId, container) {
        try {
            const response = await fetch(`/season/${seasonId}/episodes/`);
            const data = await response.json();

            if (data.episodes && data.episodes.length > 0) {
                let episodesHtml = '<div class="table-responsive"><table class="table table-sm mb-0">';
                episodesHtml += '<thead class="table-light">';
                episodesHtml += '<tr><th>Episode</th><th>Title</th><th>Air Date</th><th>Runtime</th><th>Quality</th><th>Status</th><th>Actions</th></tr>';
                episodesHtml += '</thead><tbody>';

                data.episodes.forEach(episode => {
                    const statusClass = episode.availability_status === 'available' ? 'available' : 'unavailable';
                    const statusBadge = episode.availability_status === 'available' ?
                        '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>Available</span>' :
                        '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>Unavailable</span>';

                    const qualityBadge = episode.quality ?
                        `<span class="badge bg-info quality-badge">${episode.quality.resolution} ${episode.quality.source}</span>` :
                        '<span class="text-muted">-</span>';

                    episodesHtml += `<tr class="episode-row ${statusClass}">`;
                    episodesHtml += `<td><strong>E${episode.episode_number.toString().padStart(2, '0')}</strong></td>`;
                    episodesHtml += `<td>${episode.name || 'Episode ' + episode.episode_number}</td>`;
                    episodesHtml += `<td>${episode.air_date ? new Date(episode.air_date).toLocaleDateString() : '-'}</td>`;
                    episodesHtml += `<td>${episode.runtime ? episode.runtime + ' min' : '-'}</td>`;
                    episodesHtml += `<td>${qualityBadge}</td>`;
                    episodesHtml += `<td>${statusBadge}</td>`;
                    episodesHtml += `<td>
                        <div class="d-flex gap-1">
                            <button class="btn btn-sm btn-outline-success"
                                    onclick="autoSearchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number}, {{ tv_show.id }})"
                                    title="Automatic search - select best torrent">
                                <i class="bi bi-search"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary"
                                    onclick="searchEpisode('{{ tv_show.title|escapejs }}', ${data.season.season_number}, ${episode.episode_number})"
                                    title="Manual search - browse all torrents">
                                <i class="bi bi-person"></i>
                            </button>
                        </div>
                    </td>`;
                    episodesHtml += '</tr>';
                });

                episodesHtml += '</tbody></table></div>';
                container.innerHTML = episodesHtml;
            } else {
                container.innerHTML = '<div class="text-center p-3"><p class="text-muted mb-0">No episodes found for this season.</p></div>';
            }

            container.setAttribute('data-loaded', 'true');

        } catch (error) {
            console.error('Error loading episodes:', error);
            container.innerHTML = '<div class="text-center p-3"><p class="text-danger mb-0">Failed to load episodes. Please try again.</p></div>';
        }
    }

    // Global variables for current search
    let currentSearchParams = null;

    // Jackett search functionality
    function searchEpisode(showTitle, season, episode) {
        // Store search parameters
        currentSearchParams = {
            type: 'episode',
            showTitle: showTitle,
            season: season,
            episode: episode
        };
        // Update modal title
        document.getElementById('searchModalTitle').textContent = 'Search for Episode';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('jackettSearchModal'));
        modal.show();

        // Update search info
        const searchQuery = `${showTitle} S${season.toString().padStart(2, '0')}E${episode.toString().padStart(2, '0')}`;
        document.getElementById('searchQuery').textContent = `Searching for: ${searchQuery}`;
        document.getElementById('searchInfo').style.display = 'block';

        // Show loading state
        resetSearchModal();
        document.getElementById('searchLoading').style.display = 'block';

        // Perform search
        performJackettSearch(showTitle, season, episode, 'episode');
    }

    function searchSeason(showTitle, season) {
        // Store search parameters
        currentSearchParams = {
            type: 'season',
            showTitle: showTitle,
            season: season
        };

        // Update modal title
        document.getElementById('searchModalTitle').textContent = 'Search for Season';

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('jackettSearchModal'));
        modal.show();

        // Update search info
        const searchQuery = `${showTitle} Season ${season}`;
        document.getElementById('searchQuery').textContent = `Searching for: ${searchQuery}`;
        document.getElementById('searchInfo').style.display = 'block';

        // Show loading state
        resetSearchModal();
        document.getElementById('searchLoading').style.display = 'block';

        // Perform search
        performJackettSeasonSearch(showTitle, season);
    }

    function resetSearchModal() {
        document.getElementById('searchLoading').style.display = 'block';
        document.getElementById('searchError').style.display = 'none';
        document.getElementById('searchResults').style.display = 'none';
        document.getElementById('noResults').style.display = 'none';
    }

    async function performJackettSearch(showTitle, season, episode, searchType = 'episode') {
        try {
            const filterEnabled = document.getElementById('filterToggle').checked;
            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                episode: episode,
                indexer: 'all',
                filter: filterEnabled ? 'true' : 'false'
            });

            const response = await fetch(`/api/jackett/search/?${params}`);
            const data = await response.json();

            // Hide loading
            document.getElementById('searchLoading').style.display = 'none';

            if (data.success && data.results && data.results.length > 0) {
                displaySearchResults(data.results, searchType, data.filtered);
            } else if (data.error) {
                showSearchError(data.error);
            } else {
                document.getElementById('noResults').style.display = 'block';
            }

        } catch (error) {
            console.error('Search error:', error);
            document.getElementById('searchLoading').style.display = 'none';
            showSearchError('Failed to search. Please check your Jackett configuration.');
        }
    }

    async function performJackettSeasonSearch(showTitle, season) {
        try {
            const filterEnabled = document.getElementById('filterToggle').checked;
            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                indexer: 'all',
                filter: filterEnabled ? 'true' : 'false'
            });

            const response = await fetch(`/api/jackett/search-season/?${params}`);
            const data = await response.json();

            // Hide loading
            document.getElementById('searchLoading').style.display = 'none';

            if (data.success && data.results && data.results.length > 0) {
                displaySearchResults(data.results, 'season', data.filtered);
            } else if (data.error) {
                showSearchError(data.error);
            } else {
                document.getElementById('noResults').style.display = 'block';
            }

        } catch (error) {
            console.error('Season search error:', error);
            document.getElementById('searchLoading').style.display = 'none';
            showSearchError('Failed to search season. Please check your Jackett configuration.');
        }
    }

    function displaySearchResults(results, searchType = 'episode', isFiltered = true) {
        const tbody = document.getElementById('resultsTableBody');
        tbody.innerHTML = '';

        // Update results count
        const searchTypeText = searchType === 'season' ? 'season packs' : 'episodes';
        document.getElementById('resultsCount').textContent = `${results.length} ${searchTypeText}`;

        // Show/hide the "Show All Results" button
        const showAllBtn = document.getElementById('showAllResultsBtn');
        if (isFiltered && results.length > 0) {
            showAllBtn.style.display = 'block';
            showAllBtn.innerHTML = '<i class="bi bi-funnel"></i> Show All Results';
        } else {
            showAllBtn.style.display = 'none';
        }

        results.forEach(result => {
            const row = document.createElement('tr');

            // Truncate title if too long
            const title = result.title.length > 60 ? result.title.substring(0, 60) + '...' : result.title;

            // Quality badge color based on quality
            let qualityBadgeClass = 'bg-info';
            if (result.quality === '4K') qualityBadgeClass = 'bg-danger';
            else if (result.quality === '1080p') qualityBadgeClass = 'bg-success';
            else if (result.quality === '720p') qualityBadgeClass = 'bg-warning';
            else if (result.quality === '480p') qualityBadgeClass = 'bg-secondary';

            row.innerHTML = `
                <td>
                    <div class="fw-bold">${title}</div>
                    <small class="text-muted">${result.description || ''}</small>
                </td>
                <td><span class="badge ${qualityBadgeClass}">${result.quality}</span></td>
                <td><strong>${result.size_formatted}</strong></td>
                <td>
                    <span class="badge bg-success">
                        <i class="bi bi-arrow-up"></i> ${result.seeders}
                    </span>
                </td>
                <td>
                    <span class="badge bg-warning">
                        <i class="bi bi-arrow-down"></i> ${result.leechers}
                    </span>
                </td>
                <td><small class="text-muted">${result.indexer}</small></td>
                <td>
                    <button class="btn btn-sm btn-primary"
                            onclick="downloadTorrent('${result.download_url}', '${result.magnet_url}', '${result.title.replace(/'/g, "\\'")}')">
                        <i class="bi bi-download"></i> Download
                    </button>
                </td>
            `;

            tbody.appendChild(row);
        });

        document.getElementById('searchResults').style.display = 'block';
    }

    function showSearchError(message) {
        document.getElementById('searchErrorMessage').textContent = message;
        document.getElementById('searchError').style.display = 'block';
    }

    async function downloadTorrent(downloadUrl, magnetUrl, title) {
        try {
            const response = await fetch('/api/jackett/download/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    download_url: downloadUrl,
                    magnet_url: magnetUrl,
                    title: title
                })
            });

            const data = await response.json();

            if (data.success) {
                alert(data.message);
            } else {
                alert('Download failed: ' + (data.error || 'Unknown error'));
            }

        } catch (error) {
            console.error('Download error:', error);
            alert('Download failed: ' + error.message);
        }
    }

    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Toggle filter function
    function toggleFilter() {
        if (!currentSearchParams) return;

        // Toggle the filter checkbox
        const filterToggle = document.getElementById('filterToggle');
        filterToggle.checked = !filterToggle.checked;

        // Re-run the search with new filter setting
        resetSearchModal();
        document.getElementById('searchLoading').style.display = 'block';

        if (currentSearchParams.type === 'episode') {
            performJackettSearch(
                currentSearchParams.showTitle,
                currentSearchParams.season,
                currentSearchParams.episode,
                'episode'
            );
        } else if (currentSearchParams.type === 'season') {
            performJackettSeasonSearch(
                currentSearchParams.showTitle,
                currentSearchParams.season
            );
        }
    }

    // Toast notification functions
    function showToast(title, message, type = 'success', duration = 5000) {
        const toast = document.getElementById('notificationToast');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');
        const toastIcon = document.getElementById('toastIcon');

        // Set content
        toastTitle.textContent = title;
        toastMessage.innerHTML = message; // Use innerHTML to support HTML content

        // Set icon and colors based on type
        if (type === 'success') {
            toastIcon.className = 'bi bi-check-circle-fill text-success me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-success-subtle';
        } else if (type === 'error') {
            toastIcon.className = 'bi bi-exclamation-triangle-fill text-danger me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-danger-subtle';
        } else if (type === 'warning') {
            toastIcon.className = 'bi bi-exclamation-triangle-fill text-warning me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-warning-subtle';
        } else if (type === 'info') {
            toastIcon.className = 'bi bi-info-circle-fill text-info me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-info-subtle';
        }

        // Show the toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: duration
        });
        bsToast.show();

        return bsToast; // Return the toast instance for manual control
    }

    // Automatic search functions
    async function autoSearchEpisode(showTitle, season, episode, showId) {
        try {
            // Show loading indicator
            const loadingToast = showToast('Searching...', 'Automatically searching for best torrent...', 'info');

            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                episode: episode,
                show_id: showId
            });

            const response = await fetch(`/api/jackett/auto-search/?${params}`);
            const data = await response.json();

            // Hide loading toast
            if (loadingToast && loadingToast.hide) {
                loadingToast.hide();
            }

            if (data.success) {
                const torrent = data.torrent;
                const message = `
                    <strong>Best torrent selected:</strong><br>
                    <strong>Title:</strong> ${torrent.title}<br>
                    <strong>Quality:</strong> ${torrent.quality}<br>
                    <strong>Size:</strong> ${torrent.size}<br>
                    <strong>Seeders:</strong> ${torrent.seeders}<br>
                    <strong>Indexer:</strong> ${torrent.indexer}<br>
                    <strong>Profile used:</strong> ${data.profile_used}<br>
                    <strong>Results:</strong> ${data.filtered_results}/${data.total_results} matching torrents
                `;

                showToast('Auto Search Complete', message, 'success', 10000);
            } else {
                showToast('Auto Search Failed', data.error || 'Unknown error occurred', 'error');
            }
        } catch (error) {
            console.error('Auto search failed:', error);
            showToast('Auto Search Error', 'Failed to perform automatic search: ' + error.message, 'error');
        }
    }

    async function autoSearchSeason(showTitle, season, showId) {
        try {
            // Show loading indicator
            const loadingToast = showToast('Searching...', 'Automatically searching for best season pack...', 'info');

            const params = new URLSearchParams({
                show_title: showTitle,
                season: season,
                show_id: showId
            });

            const response = await fetch(`/api/jackett/auto-search-season/?${params}`);
            const data = await response.json();

            // Hide loading toast
            if (loadingToast && loadingToast.hide) {
                loadingToast.hide();
            }

            if (data.success) {
                const torrent = data.torrent;
                const message = `
                    <strong>Best season pack selected:</strong><br>
                    <strong>Title:</strong> ${torrent.title}<br>
                    <strong>Quality:</strong> ${torrent.quality}<br>
                    <strong>Size:</strong> ${torrent.size}<br>
                    <strong>Seeders:</strong> ${torrent.seeders}<br>
                    <strong>Indexer:</strong> ${torrent.indexer}<br>
                    <strong>Profile used:</strong> ${data.profile_used}<br>
                    <strong>Results:</strong> ${data.filtered_results}/${data.total_results} matching torrents
                `;

                showToast('Auto Search Complete', message, 'success', 10000);
            } else {
                showToast('Auto Search Failed', data.error || 'Unknown error occurred', 'error');
            }
        } catch (error) {
            console.error('Auto season search failed:', error);
            showToast('Auto Search Error', 'Failed to perform automatic season search: ' + error.message, 'error');
        }
    }

    // Profile management functions
    async function loadAvailableProfiles() {
        try {
            const response = await fetch('/api/profiles/');
            const data = await response.json();

            if (data.success) {
                const container = document.getElementById('profileDropdownItems');
                container.innerHTML = '';

                data.profiles.forEach(profile => {
                    const li = document.createElement('li');
                    const isActive = {{ tv_show.quality_profile.id|default:'null' }} === profile.id;

                    li.innerHTML = `
                        <a class="dropdown-item ${isActive ? 'active' : ''}"
                           href="#" onclick="assignProfile(${profile.id})">
                            <i class="bi bi-sliders me-1"></i>${profile.name}
                            ${profile.is_default ? '<span class="badge bg-primary ms-1">Default</span>' : ''}
                        </a>
                    `;

                    container.appendChild(li);
                });
            }
        } catch (error) {
            console.error('Failed to load profiles:', error);
        }
    }

    async function assignProfile(profileId) {
        try {
            const response = await fetch(`/shows/{{ tv_show.id }}/assign-profile/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    profile_id: profileId
                })
            });

            const data = await response.json();

            if (data.success) {
                // Reload the page to update the profile display
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        } catch (error) {
            console.error('Failed to assign profile:', error);
            alert('Failed to assign profile: ' + error.message);
        }
    }

    // Load available profiles on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadAvailableProfiles();
    });
</script>
{% endblock %}
