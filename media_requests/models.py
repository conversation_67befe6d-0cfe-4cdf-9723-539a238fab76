from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator


class MediaQuality(models.Model):
    """Model to store quality information for media content"""
    resolution = models.CharField(max_length=20, help_text="e.g., 1080p, 720p, 4K")
    source = models.CharField(max_length=50, help_text="e.g., BluRay, WEB-DL, HDTV")
    codec = models.CharField(max_length=20, help_text="e.g., x264, x265, AV1")

    class Meta:
        verbose_name_plural = "Media Qualities"
        unique_together = ['resolution', 'source', 'codec']

    def __str__(self):
        return f"{self.resolution} {self.source} {self.codec}"


class MediaRequest(models.Model):
    """Model to store Overseerr media request metadata"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('declined', 'Declined'),
        ('available', 'Available'),
    ]

    overseerr_id = models.IntegerField(unique=True, help_text="ID from Overseerr API")
    media_type = models.CharField(max_length=10, choices=[('tv', 'TV Show'), ('movie', 'Movie')])
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    requested_by = models.CharField(max_length=100)
    requested_at = models.DateTimeField()
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-requested_at']

    def __str__(self):
        return f"Request #{self.overseerr_id} - {self.media_type}"


class TVShow(models.Model):
    """Model to store TV show information"""
    AVAILABILITY_CHOICES = [
        ('unavailable', 'Unavailable'),
        ('partial', 'Partially Available'),
        ('available', 'Fully Available'),
    ]

    # Basic show information
    tmdb_id = models.IntegerField(unique=True, help_text="TMDB ID")
    title = models.CharField(max_length=200)
    overview = models.TextField(blank=True)
    poster_path = models.CharField(max_length=200, blank=True)
    backdrop_path = models.CharField(max_length=200, blank=True)
    first_air_date = models.DateField(null=True, blank=True)
    last_air_date = models.DateField(null=True, blank=True)

    # Status and availability
    status = models.CharField(max_length=50, blank=True)  # e.g., "Ended", "Returning Series"
    availability_status = models.CharField(max_length=20, choices=AVAILABILITY_CHOICES, default='unavailable')

    # Plex integration
    plex_rating_key = models.CharField(max_length=50, blank=True, help_text="Plex rating key for direct API access")

    # Relationships
    media_request = models.OneToOneField(MediaRequest, on_delete=models.CASCADE, null=True, blank=True)
    quality_profile = models.ForeignKey('QualityProfile', on_delete=models.SET_NULL, null=True, blank=True, help_text="Quality profile for this show")

    # Direct addition fields (when not from Overseerr request)
    added_by = models.CharField(max_length=100, blank=True, help_text="User who added this show directly")
    added_at = models.DateTimeField(null=True, blank=True, help_text="When this show was added directly")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['title']

    def __str__(self):
        return self.title

    def get_total_seasons(self):
        return self.seasons.count()

    def get_available_seasons(self):
        return self.seasons.filter(availability_status='available').count()

    def get_total_episodes(self):
        return sum(season.episodes.count() for season in self.seasons.all())

    def get_available_episodes(self):
        return sum(season.episodes.filter(availability_status='available').count() for season in self.seasons.all())

    def get_primary_quality(self):
        """Get the most common quality for this show"""
        from django.db.models import Count

        # Get the most common quality among available episodes
        quality_counts = (
            Episode.objects
            .filter(season__tv_show=self, availability_status='available', quality__isnull=False)
            .values('quality__resolution')
            .annotate(count=Count('quality__resolution'))
            .order_by('-count')
            .first()
        )

        if quality_counts:
            return quality_counts['quality__resolution']
        return None


class Season(models.Model):
    """Model to store season information for TV shows"""
    AVAILABILITY_CHOICES = [
        ('unavailable', 'Unavailable'),
        ('partial', 'Partially Available'),
        ('available', 'Fully Available'),
    ]

    # Basic season information
    tv_show = models.ForeignKey(TVShow, on_delete=models.CASCADE, related_name='seasons')
    season_number = models.IntegerField()
    name = models.CharField(max_length=200, blank=True)
    overview = models.TextField(blank=True)
    poster_path = models.CharField(max_length=200, blank=True)
    air_date = models.DateField(null=True, blank=True)

    # Status and availability
    availability_status = models.CharField(max_length=20, choices=AVAILABILITY_CHOICES, default='unavailable')
    quality = models.ForeignKey(MediaQuality, on_delete=models.SET_NULL, null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['season_number']
        unique_together = ['tv_show', 'season_number']

    def __str__(self):
        return f"{self.tv_show.title} - Season {self.season_number}"

    def get_total_episodes(self):
        return self.episodes.count()

    def get_available_episodes(self):
        return self.episodes.filter(availability_status='available').count()

    def update_availability_status(self):
        """Update availability status based on episode availability"""
        total_episodes = self.get_total_episodes()
        available_episodes = self.get_available_episodes()

        if available_episodes == 0:
            self.availability_status = 'unavailable'
        elif available_episodes == total_episodes:
            self.availability_status = 'available'
        else:
            self.availability_status = 'partial'

        self.save()


class Episode(models.Model):
    """Model to store episode information"""
    AVAILABILITY_CHOICES = [
        ('unavailable', 'Unavailable'),
        ('available', 'Available'),
    ]

    # Basic episode information
    season = models.ForeignKey(Season, on_delete=models.CASCADE, related_name='episodes')
    episode_number = models.IntegerField()
    name = models.CharField(max_length=200, blank=True)
    overview = models.TextField(blank=True)
    still_path = models.CharField(max_length=200, blank=True)
    air_date = models.DateField(null=True, blank=True)
    runtime = models.IntegerField(null=True, blank=True, help_text="Runtime in minutes")

    # Status and availability
    availability_status = models.CharField(max_length=20, choices=AVAILABILITY_CHOICES, default='unavailable')
    quality = models.ForeignKey(MediaQuality, on_delete=models.SET_NULL, null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['episode_number']
        unique_together = ['season', 'episode_number']

    def __str__(self):
        return f"{self.season.tv_show.title} S{self.season.season_number:02d}E{self.episode_number:02d} - {self.name}"

    @property
    def tv_show(self):
        return self.season.tv_show


# Quality Profile Models

class QualityDefinition(models.Model):
    """Defines available quality levels and their properties"""

    name = models.CharField(max_length=50, unique=True)
    resolution = models.CharField(max_length=20)  # e.g., "1080p", "720p", "4K"
    source = models.CharField(max_length=20, blank=True)  # e.g., "WEB-DL", "BluRay", "HDTV"
    priority = models.IntegerField(default=0, help_text="Higher number = higher priority")
    min_size_mb = models.IntegerField(default=0, help_text="Minimum file size in MB")
    max_size_mb = models.IntegerField(default=0, help_text="Maximum file size in MB (0 = unlimited)")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', 'name']

    def __str__(self):
        if self.source:
            return f"{self.resolution} {self.source}"
        return self.resolution


class QualityProfile(models.Model):
    """Quality profile that defines upgrade behavior and allowed qualities"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    allow_upgrades = models.BooleanField(default=True, help_text="Allow upgrading to better quality")
    upgrade_until_quality = models.ForeignKey(
        QualityDefinition,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='upgrade_until_profiles',
        help_text="Stop upgrading once this quality is reached"
    )
    minimum_custom_format_score = models.IntegerField(
        default=0,
        help_text="Minimum score required for downloads"
    )
    upgrade_until_custom_format_score = models.IntegerField(
        default=10000,
        help_text="Stop upgrading once this score is reached"
    )
    is_default = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # Ensure only one default profile
        if self.is_default:
            QualityProfile.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


class QualityProfileItem(models.Model):
    """Links quality definitions to profiles with specific settings"""

    profile = models.ForeignKey(QualityProfile, on_delete=models.CASCADE, related_name='quality_items')
    quality = models.ForeignKey(QualityDefinition, on_delete=models.CASCADE)
    allowed = models.BooleanField(default=True, help_text="Is this quality allowed for download")
    order = models.IntegerField(default=0, help_text="Order within the profile (higher = preferred)")

    class Meta:
        unique_together = ['profile', 'quality']
        ordering = ['-order', 'quality__priority']

    def __str__(self):
        return f"{self.profile.name} - {self.quality.name}"


class CustomFormat(models.Model):
    """Custom format definitions for advanced quality scoring"""

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    include_patterns = models.JSONField(
        default=list,
        help_text="Patterns that must be present (regex supported)"
    )
    exclude_patterns = models.JSONField(
        default=list,
        help_text="Patterns that must NOT be present (regex supported)"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name


class CustomFormatProfileScore(models.Model):
    """Scoring for custom formats within a quality profile"""

    profile = models.ForeignKey(QualityProfile, on_delete=models.CASCADE, related_name='custom_format_scores')
    custom_format = models.ForeignKey(CustomFormat, on_delete=models.CASCADE)
    score = models.IntegerField(
        default=0,
        validators=[MinValueValidator(-10000), MaxValueValidator(10000)],
        help_text="Score for this custom format (-10000 to 10000)"
    )

    class Meta:
        unique_together = ['profile', 'custom_format']
        ordering = ['-score', 'custom_format__name']

    def __str__(self):
        return f"{self.profile.name} - {self.custom_format.name}: {self.score}"
