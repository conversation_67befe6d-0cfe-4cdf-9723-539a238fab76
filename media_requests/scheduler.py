"""
Scheduler for automatic Overseerr data synchronization
"""

import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from django.conf import settings
from django.core.management import call_command
from django.utils import timezone

logger = logging.getLogger(__name__)

class OverseerrScheduler:
    """Scheduler for automatic Overseerr sync operations"""
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
    
    def start(self):
        """Start the scheduler"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        try:
            self.scheduler = BackgroundScheduler()
            
            # Add hourly sync job
            self.scheduler.add_job(
                func=self.sync_overseerr_data,
                trigger=IntervalTrigger(hours=1),
                id='overseerr_sync',
                name='Overseerr Data Sync',
                replace_existing=True,
                max_instances=1,  # Prevent overlapping jobs
                misfire_grace_time=300  # 5 minutes grace time
            )
            
            self.scheduler.start()
            self.is_running = True
            logger.info("Overseerr scheduler started - sync will run every hour")
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            raise
    
    def stop(self):
        """Stop the scheduler"""
        if self.scheduler and self.is_running:
            try:
                self.scheduler.shutdown(wait=False)
                self.is_running = False
                logger.info("Overseerr scheduler stopped")
            except Exception as e:
                logger.error(f"Error stopping scheduler: {e}")
    
    def sync_overseerr_data(self):
        """Execute the Plex data sync"""
        try:
            logger.info("Starting scheduled Plex data sync")

            # Check if API is configured
            if not settings.OVERSEERR_API_KEY or not settings.OVERSEERR_BASE_URL:
                logger.warning("Overseerr API not configured, skipping sync")
                return

            # Call the management command (now syncs Plex data only)
            call_command('sync_overseerr')
            logger.info("Scheduled Plex data sync completed successfully")

        except Exception as e:
            logger.error(f"Scheduled Plex data sync failed: {e}")
    
    def get_status(self):
        """Get scheduler status"""
        if not self.scheduler:
            return {
                'running': False,
                'jobs': [],
                'next_run': None
            }
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return {
            'running': self.is_running,
            'jobs': jobs,
            'next_run': jobs[0]['next_run'] if jobs else None
        }

# Global scheduler instance
_scheduler = None

def get_scheduler():
    """Get the global scheduler instance"""
    global _scheduler
    if _scheduler is None:
        _scheduler = OverseerrScheduler()
    return _scheduler

def start_scheduler():
    """Start the global scheduler"""
    scheduler = get_scheduler()
    scheduler.start()

def stop_scheduler():
    """Stop the global scheduler"""
    scheduler = get_scheduler()
    scheduler.stop()

def get_scheduler_status():
    """Get the status of the global scheduler"""
    scheduler = get_scheduler()
    return scheduler.get_status()
