from django.urls import path
from . import views, profile_views

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('show/<int:show_id>/', views.show_detail, name='show_detail'),
    path('season/<int:season_id>/episodes/', views.season_episodes_ajax, name='season_episodes_ajax'),
    path('sync/', views.sync_data, name='sync_data'),
    path('api/status/', views.api_status, name='api_status'),
    path('api/scheduler/', views.scheduler_status, name='scheduler_status'),
    path('api/search/', views.search_shows, name='search_shows'),
    path('api/add/', views.add_show, name='add_show'),
    path('api/delete/<int:show_id>/', views.delete_show, name='delete_show'),
    path('api/jackett/search/', views.jackett_search_episode, name='jackett_search_episode'),
    path('api/jackett/search-season/', views.jackett_search_season, name='jackett_search_season'),
    path('api/jackett/auto-search/', views.jackett_auto_search_episode, name='jackett_auto_search_episode'),
    path('api/jackett/auto-search-season/', views.jackett_auto_search_season, name='jackett_auto_search_season'),
    path('api/jackett/status/', views.jackett_status, name='jackett_status'),
    path('api/jackett/download/', views.jackett_download, name='jackett_download'),
    path('api/jackett/test/', views.jackett_test_search, name='jackett_test_search'),

    # API endpoints
    path('api/profiles/', views.api_profiles_list, name='api_profiles_list'),

    # Profile URLs
    path('profiles/', profile_views.profiles_list, name='profiles_list'),
    path('profiles/create/', profile_views.profile_create, name='profile_create'),
    path('profiles/<int:profile_id>/', profile_views.profile_detail, name='profile_detail'),
    path('profiles/<int:profile_id>/update/', profile_views.profile_update, name='profile_update'),
    path('profiles/<int:profile_id>/delete/', profile_views.profile_delete, name='profile_delete'),
    path('profiles/<int:profile_id>/api/', profile_views.profile_api_data, name='profile_api_data'),
    path('shows/<int:show_id>/assign-profile/', profile_views.assign_profile_to_show, name='assign_profile_to_show'),

    # Custom Format URLs
    path('profiles/custom-formats/', profile_views.custom_formats_list, name='custom_formats_list'),
    path('profiles/custom-formats/create/', profile_views.custom_format_create, name='custom_format_create'),
    path('profiles/custom-formats/<int:format_id>/update/', profile_views.custom_format_update, name='custom_format_update'),
    path('profiles/custom-formats/<int:format_id>/delete/', profile_views.custom_format_delete, name='custom_format_delete'),
    path('profiles/custom-formats/<int:format_id>/api/', profile_views.custom_format_api_data, name='custom_format_api_data'),
]
