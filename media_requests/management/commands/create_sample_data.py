from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, timedelta
from media_requests.models import MediaRequest, TVShow, Season, Episode, MediaQuality


class Command(BaseCommand):
    help = 'Create sample data for testing the application'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create sample qualities
        quality_1080p = MediaQuality.objects.get_or_create(
            resolution='1080p',
            source='WEB-DL',
            codec='x264'
        )[0]
        
        quality_720p = MediaQuality.objects.get_or_create(
            resolution='720p',
            source='HDTV',
            codec='x264'
        )[0]
        
        # Create sample media requests
        request1 = MediaRequest.objects.get_or_create(
            overseerr_id=1001,
            defaults={
                'media_type': 'tv',
                'status': 'approved',
                'requested_by': '<PERSON>',
                'requested_at': timezone.now() - timedelta(days=30),
            }
        )[0]
        
        request2 = MediaRequest.objects.get_or_create(
            overseerr_id=1002,
            defaults={
                'media_type': 'tv',
                'status': 'available',
                'requested_by': '<PERSON>',
                'requested_at': timezone.now() - timedelta(days=15),
            }
        )[0]
        
        # Create sample TV shows
        show1 = TVShow.objects.get_or_create(
            tmdb_id=12345,
            defaults={
                'title': 'Breaking Bad',
                'overview': 'A high school chemistry teacher diagnosed with inoperable lung cancer turns to manufacturing and selling methamphetamine in order to secure his family\'s future.',
                'poster_path': '/ggFHVNu6YYI5L9pCfOacjizRGt.jpg',
                'first_air_date': date(2008, 1, 20),
                'last_air_date': date(2013, 9, 29),
                'status': 'Ended',
                'availability_status': 'available',
                'media_request': request1,
            }
        )[0]
        
        show2 = TVShow.objects.get_or_create(
            tmdb_id=67890,
            defaults={
                'title': 'The Office',
                'overview': 'A mockumentary on a group of typical office workers, where the workday consists of ego clashes, inappropriate behavior, and tedium.',
                'poster_path': '/7DJKHzAi83BmQrWLrYYOqcoKfhR.jpg',
                'first_air_date': date(2005, 3, 24),
                'last_air_date': date(2013, 5, 16),
                'status': 'Ended',
                'availability_status': 'partial',
                'media_request': request2,
            }
        )[0]
        
        # Create seasons for Breaking Bad
        season1 = Season.objects.get_or_create(
            tv_show=show1,
            season_number=1,
            defaults={
                'name': 'Season 1',
                'overview': 'Walter White, a struggling high school chemistry teacher, is diagnosed with advanced lung cancer.',
                'air_date': date(2008, 1, 20),
                'availability_status': 'available',
                'quality': quality_1080p,
            }
        )[0]
        
        season2 = Season.objects.get_or_create(
            tv_show=show1,
            season_number=2,
            defaults={
                'name': 'Season 2',
                'overview': 'Walt and Jesse attempt to tie up loose ends.',
                'air_date': date(2009, 3, 8),
                'availability_status': 'available',
                'quality': quality_1080p,
            }
        )[0]
        
        # Create seasons for The Office
        office_season1 = Season.objects.get_or_create(
            tv_show=show2,
            season_number=1,
            defaults={
                'name': 'Season 1',
                'overview': 'The first season of The Office.',
                'air_date': date(2005, 3, 24),
                'availability_status': 'available',
                'quality': quality_720p,
            }
        )[0]
        
        office_season2 = Season.objects.get_or_create(
            tv_show=show2,
            season_number=2,
            defaults={
                'name': 'Season 2',
                'overview': 'The second season of The Office.',
                'air_date': date(2005, 9, 20),
                'availability_status': 'unavailable',
            }
        )[0]
        
        # Create episodes for Breaking Bad Season 1
        episodes_s1 = [
            ('Pilot', 'Walter White, a struggling high school chemistry teacher, is diagnosed with advanced lung cancer.'),
            ('Cat\'s in the Bag...', 'Walt and Jesse attempt to tie up loose ends.'),
            ('...And the Bag\'s in the River', 'Walt and Jesse clean up after the bathtub incident.'),
        ]
        
        for i, (title, overview) in enumerate(episodes_s1, 1):
            Episode.objects.get_or_create(
                season=season1,
                episode_number=i,
                defaults={
                    'name': title,
                    'overview': overview,
                    'air_date': date(2008, 1, 20) + timedelta(days=(i-1)*7),
                    'runtime': 47,
                    'availability_status': 'available',
                    'quality': quality_1080p,
                }
            )
        
        # Create episodes for The Office Season 1
        office_episodes = [
            ('Pilot', 'A documentary crew arrives at the Scranton branch of the Dunder Mifflin paper company.'),
            ('Diversity Day', 'Michael\'s off-color remark puts a sensitivity trainer in the office.'),
            ('Health Care', 'Michael leaves it up to the staff to choose their own health care plan.'),
        ]
        
        for i, (title, overview) in enumerate(office_episodes, 1):
            Episode.objects.get_or_create(
                season=office_season1,
                episode_number=i,
                defaults={
                    'name': title,
                    'overview': overview,
                    'air_date': date(2005, 3, 24) + timedelta(days=(i-1)*7),
                    'runtime': 22,
                    'availability_status': 'available',
                    'quality': quality_720p,
                }
            )
        
        # Update availability statuses
        for season in [season1, season2, office_season1, office_season2]:
            season.update_availability_status()
        
        for show in [show1, show2]:
            show.save()  # This will trigger the availability status update
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample data!')
        )
